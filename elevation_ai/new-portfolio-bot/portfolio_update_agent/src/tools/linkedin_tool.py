import os
import time
import requests
import pandas as pd
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from typing import List, Dict
from langchain_core.tools import StructuredTool
from src.utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

PHANTOMBUSTER_API_KEY = os.getenv("PHANTOMBUSTER_API_KEY")
SHEET_URL = os.getenv("SHEET_URL")
HEADERS = {"X-Phantombuster-Key-1": PHANTOMBUSTER_API_KEY}
DWNLLOAD_DIR = "downloads"
PHANTOMS = {
    "company_url_finder": "company_url_finder",
    "employee_export": "employee_export",
    "activity_exnuler": "activity_exnuler"
}

async def get_sheet():
    scopes = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds = ServiceAccountCredentials.from_json_keyfile_name("config/credentials.json", scopes)
    client = gspread.authorize(creds)
    return client.open_by_url(SHEET_URL).sheet1

async def clear_and_write(sheet, company: str):
    sheet.clear()
    sheet.append_row([company.strip()])
    logger.info(f"Sheet updated for {company}")

async def launch_agent(agent_id: str, payload: dict = None):
    url = f"https://api.phantombuster.com/api/v1/agent/{agent_id}/launch"
    res = requests.post(url, headers=HEADERS, json=payload or {})
    res.raise_for_status()
    cid = res.json().get("data", {}).get("containerId")
    if not cid:
        raise Exception(f"No containerId returned for agent {agent_id}")
    logger.info(f"Launched agent {agent_id} (container {cid})")
    return cid

async def download_via_s3(agent_id: str) -> str:
    r = requests.get(f"https://api.phantombuster.com/api/v2/agents/fetch", headers=HEADERS, params={"id": agent_id})
    r.raise_for_status()
    res = r.json()
    s3 = res.get("s3Folder")
    org = res.get("orgS3Folder")
    if not s3 or not org:
        raise Exception("Missing s3Folder/orgS3Folder - results not ready")
    url = f"https://phantombuster.s3.amazonaws.com/{org}/{s3}/result.csv"
    logger.info(f"Downloading CSV from S3 {url}")
    resp = requests.get(url)
    resp.raise_for_status()
    os.makedirs(DWNLLOAD_DIR, exist_ok=True)
    path = os.path.join(DWNLLOAD_DIR, "latest.csv")
    with open(path, "wb") as f:
        f.write(resp.content)
    logger.info("CSV downloaded successfully")
    return path

async def linkedin_tool(company_name: str) -> List[Dict]:
    logger.info(f"Processing LinkedIn data for {company_name}")
    try:
        sheet = await get_sheet()
        await clear_and_write(sheet, company_name)
        await launch_agent(PHANTOMS["company_url_finder"], {"argument": {"spreadsheetUrl": SHEET_URL}})
        await launch_agent(PHANTOMS["employee_export"])
        await launch_agent(PHANTOMS["activity_exnuler"])
        time.sleep(120)  # Wait for PhantomBuster processing
        result_path = await download_via_s3(PHANTOMS["activity_exnuler"])
        df = pd.read_csv(result_path)
        profile_count = len(df)
        logger.info(f"Found {profile_count} LinkedIn profiles for {company_name}")
        linkedin_data = []
        for _, row in df.iterrows():
            profile_name = row.get("fullName", "Unknown")
            profile_url = row.get("profileUrl", "")
            post_content = row.get("postText", "")
            post_date = row.get("timestamp", "")
            linkedin_data.append({
                "title": profile_name,
                "headline": profile_name,
                "published": post_date,
                "date": post_date,
                "url": profile_url,
                "description": post_content[:200] + "..." if len(post_content) > 200 else post_content,
                "source": "linkedin",
                "full_content": post_content
            })
        return linkedin_data
    except Exception as e:
        logger.error(f"Error processing LinkedIn for {company_name}: {e}")
        return []

linkedin_tool = StructuredTool.from_function(
    func=linkedin_tool,
    name="LinkedInTool",
    description="Fetches LinkedIn data for a company"
)