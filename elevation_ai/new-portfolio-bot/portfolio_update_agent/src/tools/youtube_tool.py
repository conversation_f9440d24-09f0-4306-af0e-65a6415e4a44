import os
import time
from typing import List, Dict
from langchain_core.tools import StructuredTool
from googleapiclient.discovery import build
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api._errors import TranscriptsDisabled, NoTranscriptFound
from src.utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY")
youtube_available = bool(YOUTUBE_API_KEY)

if youtube_available:
    try:
        youtube = build("youtube", "v3", developerKey=YOUTUBE_API_KEY)
        logger.info("YouTube API initialized successfully")
    except Exception as e:
        logger.error(f"YouTube API initialization failed: {e}")
        youtube_available = False
else:
    logger.warning("YouTube API key not found in .env file. YouTube scraping will be limited")
    youtube = None

async def get_transcript(video_id: str) -> str | None:
    if not youtube_available:
        return None
    try:
        transcript = YouTubeTranscriptApi.get_transcript(video_id)
        return " ".join([x["text"] for x in transcript])
    except (TranscriptsDisabled, NoTranscriptFound):
        return None
    except Exception as e:
        logger.error(f"Error fetching transcript for video {video_id}: {e}")
        return None

async def get_channel_subscribers(youtube, channel_id: str) -> int:
    try:
        response = youtube.channels().list(part="statistics", id=channel_id).execute()
        if response.get("items"):
            return int(response["items"][0]["statistics"].get("subscriberCount", 0))
        return 0
    except Exception as e:
        logger.error(f"Error fetching subscribers for channel {channel_id}: {e}")
        return 0

async def search_top_youtube_videos(query: str, max_results: int = 50) -> List[Dict]:
    if not youtube_available or not YOUTUBE_API_KEY:
        logger.warning("YouTube API not available. Skipping YouTube search.")
        return []
    try:
        request = youtube.search().list(
            q=query,
            part="snippet",
            type="video",
            order="relevance",
            maxResults=min(max_results, 50)
        )
        response = request.execute()
        results = []
        for item in response.get("items", []):
            if "id" not in item or "videoId" not in item["id"]:
                logger.warning(f"Skipping item without videoId: {item}")
                continue
            video_id = item["id"]["videoId"]
            title = item["snippet"]["title"]
            channel_title = item["snippet"]["channelTitle"]
            channel_id = item["snippet"]["channelId"]
            published_at = item["snippet"]["publishedAt"]
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            description = item["snippet"].get("description", "")
            transcript = await get_transcript(video_id)
            subs = await get_channel_subscribers(youtube, channel_id)
            if subs < 100000:
                continue
            results.append({
                "title": title,
                "headline": title,
                "channel": channel_title,
                "published": published_at,
                "date": published_at,
                "url": video_url,
                "description": description[:200] + "..." if len(description) > 200 else description,
                "transcript": transcript,
                "full_content": transcript,
                "source": "youtube",
                "subscriber_count": subs
            })
            time.sleep(0.5)  # Respect API rate limits
        return results
    except Exception as e:
        logger.error(f"Error during YouTube API call: {e}")
        return []

async def youtube_tool(company_name: str) -> List[Dict]:
    logger.info(f"Processing YouTube videos for: {company_name}")
    videos = await search_top_youtube_videos(company_name, max_results=50)
    logger.info(f"Found {len(videos)} YouTube videos for {company_name}")
    return videos

youtube_tool = StructuredTool.from_function(
    func=youtube_tool,
    name="YouTubeTool",
    description="Fetches YouTube videos for a company"
)