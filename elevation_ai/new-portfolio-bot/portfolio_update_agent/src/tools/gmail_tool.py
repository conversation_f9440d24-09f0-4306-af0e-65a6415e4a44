import os
import base64
from typing import List, Dict
from googleapiclient.discovery import build
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from langchain_core.tools import StructuredTool
from src.utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

async def extract_emails_for_company(user_tag: str, company_name: str) -> List[Dict]:
    token_file = f"token_{user_tag.replace('@', '_')}.json"
    creds = None
    if os.path.exists(token_file):
        creds = Credentials.from_authorized_user_file(token_file)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file("config/credentials.json", ["https://www.googleapis.com/auth/gmail.readonly"])
            creds = flow.run_local_server(port=0)
        with open(token_file, "w") as token:
            token.write(creds.to_json())
    service = build("gmail", "v1", credentials=creds)
    try:
        query = f"from: {company_name.lower()}"
        results = service.users().messages().list(userId="me", q=query, maxResults=10).execute()
        messages = results.get("messages", [])
        emails = []
        for msg in messages:
            msg_data = service.users().messages().get(userId="me", id=msg["id"]).execute()
            headers = {h["name"]: h["value"] for h in msg_data["payload"]["headers"]}
            subject = headers.get("Subject", "No subject")
            date = headers.get("Date", "")
            for part in msg_data["payload"].get("parts", []):
                if part["mimeType"] == "text/plain":
                    data = part.get("body", {}).get("data")
                    if data:
                        decoded_data = base64.urlsafe_b64decode(data).decode()
                        emails.append({
                            "sender": headers.get("From", ""),
                            "headline": subject,
                            "date": date,
                            "description": decoded_data[:200] + "..." if len(decoded_data) > 200 else decoded_data,
                            "source": "gmail",
                            "full_content": decoded_data
                        })
                        break
        logger.info(f"Found {len(emails)} Gmail messages for {company_name}")
        return emails
    except Exception as e:
        logger.error(f"Error fetching Gmail for {company_name}: {e}")
        return []

async def gmail_tool(company_name: str) -> List[Dict]:
    logger.info(f"Processing Gmail data for {company_name}")
    user_tag = "<EMAIL>"
    emails = await extract_emails_for_company(user_tag, company_name)
    return emails

gmail_tool = StructuredTool.from_function(
    func=gmail_tool,
    name="GmailTool",
    description="Fetches Gmail messages for a company"
)