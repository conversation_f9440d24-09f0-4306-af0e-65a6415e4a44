import mysql.connector
import os
from typing import Dict
from src.utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

MYSQL_HOST = os.getenv("MYSQL_HOST")
MYSQL_USER = os.getenv("MYSQL_USER")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD")
MYSQL_DATABASE = os.getenv("MYSQL_DATABASE")

async def init_db():
    conn = mysql.connector.connect(
        host=MYSQL_HOST,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DATABASE
    )
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS reports (
            execution_id VARCHAR(36) PRIMARY KEY,
            company_name VARCHA<PERSON>(255),
            report_text TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    conn.commit()
    cursor.close()
    conn.close()

async def save_report(reports: Dict[str, str], execution_id: str):
    conn = mysql.connector.connect(
        host=MYSQL_HOST,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DATABASE
    )
    cursor = conn.cursor()
    for company, report in reports.items():
        cursor.execute(
            "INSERT INTO reports (execution_id, company_name, report_text) VALUES (%s, %s, %s)",
            (execution_id, company, report)
        )
    conn.commit()
    cursor.close()
    conn.close()
    logger.info(f"Saved reports for execution_id: {execution_id}")