import json
from typing import Dict
from langchain_openai import ChatOpenAI
from src.utils.logging import get_logger
from src.workflows.main_workflow import AgentState

logger = get_logger(__name__)

async def generate_report(state: AgentState) -> AgentState:
    llm = ChatOpenAI(model="gpt-4o", api_key=os.getenv("OPENAI_API_KEY"))
    with open("prompts/report_prompt.txt", "r") as f:
        prompt_template = f.read()

    state["final_report"] = {}
    for company in state["company_names"]:
        data = state["analyzed_data"].get(company, [])
        prompt = prompt_template.format(company_name=company, data=json.dumps(data), config=json.dumps(state["config"]))
        report = await llm.ainvoke(prompt)
        state["final_report"][company] = report.content

    if state["config"]["report_type"] == "combined":
        combined_prompt = prompt_template.format(
            company_name="Combined Report",
            data=json.dumps(state["analyzed_data"]),
            config=json.dumps(state["config"])
        )
        combined_report = await llm.ainvoke(combined_prompt)
        state["final_report"]["combined"] = combined_report.content

    return state