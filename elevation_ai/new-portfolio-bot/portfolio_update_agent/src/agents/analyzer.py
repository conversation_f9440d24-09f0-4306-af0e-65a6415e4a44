import json
from typing import List, Dict
from langchain_openai import ChatOpenAI
from src.utils.logging import get_logger
from src.workflows.main_workflow import AgentState

logger = get_logger(__name__)

async def analyze_data(state: AgentState) -> AgentState:
    llm = ChatOpenAI(model="gpt-4o", api_key=os.getenv("OPENAI_API_KEY"))
    with open("prompts/analysis_prompt.txt", "r") as f:
        prompt_template = f.read()

    state["analyzed_data"] = {}
    for company in state["company_names"]:
        data = state["collected_data"].get(company, [])
        prompt = prompt_template.format(company_name=company, data=json.dumps(data))
        response = await llm.ainvoke(prompt)
        try:
            analyzed = json.loads(response.content)
            state["analyzed_data"][company] = analyzed["analyzed_data"]
            if analyzed["incomplete_data"]:
                state["iteration_count"][company] += 1
        except json.JSONDecodeError:
            logger.error(f"Failed to parse analysis for {company}: {response.content}")
    return state