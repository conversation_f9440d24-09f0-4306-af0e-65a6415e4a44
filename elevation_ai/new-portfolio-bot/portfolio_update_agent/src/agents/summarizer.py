import json
from typing import List, Dict
from langchain_openai import ChatOpenAI
from src.utils.logging import get_logger
from src.workflows.main_workflow import AgentState

logger = get_logger(__name__)

async def summarize_data(state: AgentState) -> AgentState:
    llm = ChatOpenAI(model="gpt-4o", api_key=os.getenv("OPENAI_API_KEY"))
    with open("prompts/summarization_prompt.txt", "r") as f:
        prompt_template = f.read()

    for company in state["company_names"]:
        data = state["collected_data"].get(company, [])
        summarized = []
        for item in data:
            prompt = prompt_template.format(company_name=company, data=json.dumps(item))
            response = await llm.ainvoke(prompt)
            try:
                summarized_item = json.loads(response.content)
                summarized.append(summarized_item)
            except json.JSONDecodeError:
                logger.error(f"Failed to parse summary for {company}: {response.content}")
        state["collected_data"][company] = summarized
    return state